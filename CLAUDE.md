# 医疗病历质控智能代理系统 - 开发指南

## 项目概述

这是一个医疗病历质控智能代理系统，旨在通过自动化构建质控规则来实现医疗文档的智能质量控制。系统能够从多种数据源（数据库、Excel文件等）导入质控规则，并自动分类生成可执行的Python代码（规则质控）或优化的LLM提示词（内涵质控）。

## 项目结构

```
Medical_QA_Agent/
├── dev_v1/
│   └── model_use.py              # LLM接口模块
├── doc/                          # 医疗质控规范文档
│   ├── 医疗病历质控规范.md        # 详细质控标准
│   ├── ICD10诊断.xlsx           # 诊断标准
│   ├── 内涵质控评分表（新华）.xls  # 评分标准
│   └── ...                      # 其他规范文档
├── config.py                     # 配置文件（待创建）
├── database/                     # 数据库相关（待创建）
├── api/                          # API接口（待创建）
├── frontend/                     # 前端界面（待创建）
├── generated_rules/              # 自动生成的质控规则（待创建）
│   ├── rule_quality_control/     # 规则质控Python文件
│   └── content_quality_control/  # 内涵质控JSON文件
└── tests/                        # 测试文件（待创建）
```

## 开发流程

### 第一阶段：数据导入与规范化
1. **Excel规则导入模块**
   - 实现Excel文件读取功能
   - 将原始规则数据规范化为标准JSON格式
   - 按病历文本名称进行规则分类
   - 为每个规则生成唯一标识名称

2. **规则分类判断**
   - 集成LLM判断规则类型（内涵质控 vs 规则质控）
   - 分类标准：是否可通过代码直接判断
   - 在config.py中配置LLM参数

### 第二阶段：质控规则自动生成
3. **规则质控代码生成**
   - 自动生成Python函数代码
   - 按病历文本分类保存到不同Python文件
   - 通过函数名区分不同质控标准
   - 实现自动化测试功能

4. **内涵质控Prompt生成**
   - 基于规则数据生成优化的LLM提示词
   - 保存为JSON格式，通过RuleName字段区分
   - 按病历文本分类存储
   - 实现自动化测试功能

### 第三阶段：API与数据存储
5. **统一质控API**
   - 使用Flask框架构建REST API
   - 入参验证：病人信息、文本名称、病历信息
   - 集成规则质控和内涵质控模块
   - 输出：总分、扣分项、扣分内容

6. **数据库存储**
   - 设计质控标准数据库结构
   - 存储内涵质控和规则质控标准
   - 支持版本控制（最多5个历史版本）
   - 实现数据CRUD操作

### 第四阶段：前端界面
7. **质控标准查看界面**
   - 按科室和病历文本名称查询
   - 展示内涵质控和规则质控详情
   - 支持版本历史查看

8. **质控标准管理界面**
   - 在线测试质控规则
   - 支持人工修改和保存
   - 版本选择和切换功能
   - 质控标准激活/停用

## 技术规范

### 代码生成规范
- **规则质控函数**：
  ```python
  def rule_name_check(patient_info, medical_record):
      """
      质控规则检查函数
      Args:
          patient_info: 病人基本信息
          medical_record: 病历内容
      Returns:
          dict: {"score": int, "deduction": list, "message": str}
      """
      pass
  ```

- **内涵质控JSON格式**：
  ```json
  {
      "RuleName": "规则名称",
      "Prompt": "优化的LLM提示词",
      "Category": "病历文本类型",
      "Version": "版本号",
      "CreateTime": "创建时间"
  }
  ```

### API接口规范
- **质控API端点**：`POST /api/quality_control`
- **请求格式**：
  ```json
  {
      "patient_info": {...},
      "document_type": "病历文本名称",
      "medical_record": "病历内容"
  }
  ```
- **响应格式**：
  ```json
  {
      "overall_score": 85,
      "deduction_items": [...],
      "deduction_details": [...],
      "status": "warning"
  }
  ```

### 数据库设计
- **质控标准表**：存储规则基本信息
- **规则质控表**：存储Python代码
- **内涵质控表**：存储LLM提示词
- **版本管理表**：记录版本历史

## 开发环境配置

### 必需依赖
```python
# requirements.txt
flask>=2.0.0
pandas>=1.3.0
openpyxl>=3.0.0
requests>=2.25.0
sqlalchemy>=1.4.0
concurrent.futures  # Python标准库
```

### 配置文件模板
```python
# config.py
class Config:
    # LLM配置
    LLM_API_URL = "https://testai.wonderscloud.com/v1/chat/completions"
    LLM_MODEL = "qwen3-32b"
    LLM_API_KEY = "your_api_key"
    
    # 数据库配置
    DATABASE_URL = "sqlite:///quality_control.db"
    
    # 文件路径配置
    RULES_DIR = "generated_rules"
    UPLOAD_DIR = "uploads"
    
    # 质控配置
    MAX_VERSIONS = 5
    DEFAULT_SCORE = 100
```

## 测试策略

### 单元测试
- 规则导入功能测试
- 代码生成功能测试
- API接口测试
- 数据库操作测试

### 集成测试
- 完整质控流程测试
- 前后端联调测试
- 性能压力测试

### 测试数据
使用doc/目录下的质控样本数据进行测试验证。

## 部署说明

### 开发环境启动
```bash
# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python init_db.py

# 启动开发服务器
python app.py
```

### 生产环境部署
- 使用Gunicorn或uWSGI作为WSGI服务器
- 配置Nginx作为反向代理
- 使用PostgreSQL或MySQL作为生产数据库

## 注意事项

1. **安全性**：
   - API接口需要添加认证机制
   - 输入数据需要严格验证
   - 生成的代码需要安全检查

2. **性能优化**：
   - LLM调用支持并发处理
   - 数据库查询优化
   - 缓存机制实现

3. **错误处理**：
   - 完善的异常捕获和处理
   - 日志记录机制
   - 用户友好的错误提示

4. **版本管理**：
   - 质控规则版本控制
   - 向后兼容性考虑
   - 升级迁移策略

## 开发命令

```bash
# 测试质控规则生成
python test_rule_generation.py

# 测试API接口
python test_api.py

# 数据库迁移
python migrate_db.py

# 质控标准导入
python import_rules.py --file rules.xlsx
```

这个项目将医疗质控专家的经验转化为自动化的质控系统，大大提高了质控效率和标准化程度。