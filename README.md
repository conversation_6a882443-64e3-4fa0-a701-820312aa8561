# 医疗病历质控智能代理系统

## 项目概述

本项目是一个智能化的医疗病历质控代理系统，旨在通过自动化构建质控规则来实现医疗文档的智能质量控制。系统的核心优势在于能够从多种数据源（数据库、Excel文件等）导入质控规则，并自动转换为可执行的代码规则或优化的大模型Prompt，从而实现灵活、可扩展的质控检查流程。

### 🔧 智能规则构建
- **多源数据导入**：支持从数据库、Excel文件等多种格式导入质控规则
- **自动代码生成**：将质控规则自动转换为可执行的Python代码
- **智能Prompt构建**：基于规则数据自动生成优化的LLM提示词
- **规则版本管理**：支持规则的版本控制和动态更新

### 🏥 医疗病历质控
- **多维度质量评估**：涵盖基础信息、诊断逻辑、临床信息、治疗过程、出院管理等核心维度
- **智能评分系统**：采用权重分配机制，提供0-100分的量化评估
- **问题分类体系**：系统化分类质控问题，提供针对性改进建议
- **严重程度分级**：将问题分为轻微、中等、严重三个级别

### 🤖 LLM集成能力
- **并发处理**：支持多任务并发调用LLM API，提高质控效率
- **温度控制**：使用0温度设置确保结果一致性
- **异常处理**：完善的错误处理和重试机制

### 📊 数据管理能力
- **规则数据导入**：支持Excel、CSV、数据库等多种数据源
- **规则解析引擎**：智能解析质控规则并转换为执行逻辑
- **配置管理**：灵活的配置管理系统，支持运行时规则调整

## 项目流程
1. 导入质控规则；
2. 自动化实现内涵质控（大模型pormpt）和规则质控（python代码）；
3. 自动化测试内涵质控和规则质控，所有测试过的质控标准存入数据库；
4. 前端可以查看内涵质控和规则质控，可以通过科室和病历文本名称进行质控标准的查询；
5. 前端可以测试已经生成的内涵质控和规则质控，如果有问题可以在前端人为修改保存；
6. 前端提供质控标准的多个历史纪录（不超过5个）可以自由选择合适的版本进行使用；
7. 质控的统一api，多种病历使用同一个api，入参包括病人的信息，文本名称和病历信息，通用文本名称从质控标准数据库中获取对于内涵质控和规则质控；
8. 质控api最后给出质控的评判，包括分数，扣分项，扣分内容。

## 项目实现步骤
1. 实现导入Excel的规则数据，然后规范化成json数据，需要根据相关的病历的文本名称进行分类，每个规则生成一个相应的名称。
2. 实现使用大模型（增加大模型的配置在config.py中）进行规则判断，是否为内涵质控标准或者是规则质控标准，分类的关键在于是否可以代码直接判断，还是需要进行大模型通过文字内容判断。
3. 实现规则质控的自动化生成和自动化测试，自动生成的内容为python代码，生成为一个python的函数。生成的python函数保存在相应的病历文本的规则质控python文件中，不同的规则质控标准通过不同的函数名区分。
4. 实现内涵质控的自动化生成和自动化测试，自动生成的内容为大模型的prompt。生成的prompt，保存在保存在相应的病历文本的内涵质控json文件中，不同的规则质控标准通过保存的json的中的RuleName字段不同进行区分。
5. 实现自动化测试规则质控和内涵质控，可以在python文件内测试并打印结果。
6. 实现一个统一的api进行病历质控，入参包括病人的信息，文本名称和病历信息，通用文本名称从质控标准数据库中获取对于内涵质控和规则质控，然后在分别通过两个python文件，内涵质控python文件和规则质控的python文件进行病历质控的运行。api需要一个flask进行判断入参是否齐全，除两个质控python文件，还需要一个另外的python文件来整合内涵质控和规则质控的结果，给出最终的结果需要包括总的分数，扣分项，扣分内容。
7. 实现数据库存储内涵质控和规则质控标准。
8. 实现一个前端可以查看内涵质控和规则质控标准。
9. 实现前端可以进行内涵质控和规则质控的测试、修改、保存和选择多个版本中的那个版本进入api使用。

## 技术架构

### 核心模块
- **LLM接口模块** (`dev_v1/model_use.py`)：封装LLM API调用逻辑
- **质控规范文档** (`doc/医疗病历质控规范.md`)：详细的质控标准和评估规则

### 技术特点
- 使用Qwen3-32B模型作为智能分析引擎
- 支持并发处理提高系统效率
- JSON格式标准化输出
- 完善的异常处理机制

## 文档资源

项目包含丰富的医疗行业标准文档：
- ICD10诊断标准
- 内涵质控评分表
- 医疗值域字典
- 上海地区病历质控标准（2015版）
- 质控规则和病历样本

## 评分体系

- **90-100分**：质量优良，通过质控
- **70-89分**：存在轻微问题，警告状态
- **0-69分**：存在严重问题，错误状态

## 输出格式

系统提供标准化的JSON格式质控结果，包括：
- 总分和状态
- 各维度详细评分
- 问题分类和严重程度
- 具体改进建议

## 应用场景

- **医院质控部门**：自动化构建和执行病历质控规则
- **医疗信息化公司**：快速部署适应不同医院标准的质控系统
- **质控规则研发**：将专家经验快速转化为可执行的质控逻辑
- **多院区管理**：统一质控标准，支持规则的集中管理和分发
- **质控标准更新**：快速响应政策变化，动态更新质控规则

## 技术要求

- Python 3.x
- requests库（用于API调用）
- concurrent.futures（用于并发处理）

---

本系统通过智能化的规则构建和自动化执行，大幅降低了医疗质控系统的开发和维护成本，让医疗机构能够快速适应不断变化的质控标准，为医疗质量管理提供灵活、高效的智能化解决方案。